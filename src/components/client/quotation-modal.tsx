'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  XMarkIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  CheckIcon,
  PlusIcon
} from '@heroicons/react/24/outline'

const quotationSchema = z.object({
  serviceId: z.string().min(1, 'Please select a service'),
  selectedOptions: z.array(z.string()).min(1, 'Please select at least one option'),
  selectedFeatures: z.array(z.string()),
  budget: z.number().min(1, 'Budget must be greater than 0'),
  timeline: z.string().min(1, 'Please specify timeline'),
  description: z.string().min(10, 'Please provide a detailed description (minimum 10 characters)'),
  contactPreference: z.enum(['email', 'phone', 'meeting']),
  urgency: z.enum(['low', 'medium', 'high'])
})

type QuotationFormData = z.infer<typeof quotationSchema>

interface QuotationModalProps {
  isOpen: boolean
  onClose: () => void
  services: any[]
  onSubmit: (data: any) => Promise<void>
}

export default function QuotationModal({ isOpen, onClose, services, onSubmit }: QuotationModalProps) {
  const [loading, setLoading] = useState(false)
  const [selectedService, setSelectedService] = useState<any>(null)
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors }
  } = useForm<QuotationFormData>({
    resolver: zodResolver(quotationSchema),
    defaultValues: {
      selectedOptions: [],
      selectedFeatures: [],
      contactPreference: 'email',
      urgency: 'medium'
    }
  })

  const watchedServiceId = watch('serviceId')
  const watchedOptions = watch('selectedOptions')
  const watchedFeatures = watch('selectedFeatures')

  useEffect(() => {
    if (watchedServiceId) {
      const service = services.find(s => s.id === watchedServiceId)
      setSelectedService(service)
      setValue('selectedOptions', [])
      setValue('selectedFeatures', [])
    }
  }, [watchedServiceId, services, setValue])

  const calculateEstimatedCost = () => {
    if (!selectedService) return 0
    
    let total = selectedService.price || 0
    
    // Add option costs
    watchedOptions.forEach(optionId => {
      const option = selectedService.serviceOptions?.find((opt: any) => opt.id === optionId)
      if (option?.price) {
        total += option.price
      }
    })
    
    return total
  }

  const toggleCategory = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId)
    } else {
      newExpanded.add(categoryId)
    }
    setExpandedCategories(newExpanded)
  }

  const toggleOption = (optionId: string) => {
    const currentOptions = watchedOptions || []
    const newOptions = currentOptions.includes(optionId)
      ? currentOptions.filter(id => id !== optionId)
      : [...currentOptions, optionId]
    setValue('selectedOptions', newOptions)
  }

  const toggleFeature = (featureId: string) => {
    const currentFeatures = watchedFeatures || []
    const newFeatures = currentFeatures.includes(featureId)
      ? currentFeatures.filter(id => id !== featureId)
      : [...currentFeatures, featureId]
    setValue('selectedFeatures', newFeatures)
  }

  const onSubmitForm = async (data: QuotationFormData) => {
    try {
      setLoading(true)
      
      const quotationData = {
        ...data,
        serviceName: selectedService?.name,
        estimatedCost: calculateEstimatedCost(),
        serviceDetails: {
          service: selectedService,
          selectedOptions: selectedService?.serviceOptions?.filter((opt: any) => 
            data.selectedOptions.includes(opt.id)
          ),
          selectedFeatures: selectedService?.serviceOptions?.flatMap((opt: any) => 
            opt.features?.filter((feat: any) => data.selectedFeatures.includes(feat.id))
          )
        }
      }

      await onSubmit(quotationData)
      reset()
      onClose()
    } catch (error) {
      console.error('Quotation submission error:', error)
      alert('Failed to submit quotation request. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // Group services by category
  const servicesByCategory = services.reduce((acc, service) => {
    const categoryName = service.category?.name || 'Other'
    if (!acc[categoryName]) {
      acc[categoryName] = []
    }
    acc[categoryName].push(service)
    return acc
  }, {} as Record<string, any[]>)

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full sm:p-6"
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <DocumentTextIcon className="h-6 w-6 text-blue-600 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">Request Service Quotation</h3>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={handleSubmit(onSubmitForm)} className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left Column - Service Selection */}
                <div className="space-y-6">
                  {/* Service Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Select Service
                    </label>
                    <div className="border border-gray-300 rounded-lg max-h-64 overflow-y-auto">
                      {Object.entries(servicesByCategory).map(([categoryName, categoryServices]) => (
                        <div key={categoryName}>
                          <button
                            type="button"
                            onClick={() => toggleCategory(categoryName)}
                            className="w-full flex items-center justify-between p-3 text-left bg-gray-50 border-b border-gray-200 hover:bg-gray-100"
                          >
                            <span className="font-medium text-gray-900">{categoryName}</span>
                            {expandedCategories.has(categoryName) ? (
                              <ChevronDownIcon className="h-4 w-4 text-gray-500" />
                            ) : (
                              <ChevronRightIcon className="h-4 w-4 text-gray-500" />
                            )}
                          </button>
                          
                          {expandedCategories.has(categoryName) && (
                            <div className="divide-y divide-gray-100">
                              {categoryServices.map((service) => (
                                <Controller
                                  key={service.id}
                                  name="serviceId"
                                  control={control}
                                  render={({ field }) => (
                                    <label className="flex items-center p-3 hover:bg-gray-50 cursor-pointer">
                                      <input
                                        type="radio"
                                        value={service.id}
                                        checked={field.value === service.id}
                                        onChange={field.onChange}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                                      />
                                      <div className="ml-3 flex-1">
                                        <div className="flex items-center justify-between">
                                          <p className="text-sm font-medium text-gray-900">{service.name}</p>
                                          <p className="text-sm text-gray-500">
                                            ${service.price?.toLocaleString() || 'Contact for pricing'}
                                          </p>
                                        </div>
                                        <p className="text-xs text-gray-500 mt-1">{service.description}</p>
                                      </div>
                                    </label>
                                  )}
                                />
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                    {errors.serviceId && (
                      <p className="mt-1 text-sm text-red-600">{errors.serviceId.message}</p>
                    )}
                  </div>

                  {/* Service Options */}
                  {selectedService?.serviceOptions && selectedService.serviceOptions.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Service Options
                      </label>
                      <div className="space-y-2 max-h-48 overflow-y-auto border border-gray-300 rounded-lg p-3">
                        {selectedService.serviceOptions.map((option: any) => (
                          <label key={option.id} className="flex items-start cursor-pointer">
                            <input
                              type="checkbox"
                              checked={watchedOptions.includes(option.id)}
                              onChange={() => toggleOption(option.id)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
                            />
                            <div className="ml-3 flex-1">
                              <div className="flex items-center justify-between">
                                <p className="text-sm font-medium text-gray-900">{option.name}</p>
                                {option.price && (
                                  <p className="text-sm text-gray-500">+${option.price.toLocaleString()}</p>
                                )}
                              </div>
                              {option.description && (
                                <p className="text-xs text-gray-500 mt-1">{option.description}</p>
                              )}
                            </div>
                          </label>
                        ))}
                      </div>
                      {errors.selectedOptions && (
                        <p className="mt-1 text-sm text-red-600">{errors.selectedOptions.message}</p>
                      )}
                    </div>
                  )}

                  {/* Features */}
                  {selectedService?.serviceOptions && watchedOptions.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Additional Features
                      </label>
                      <div className="space-y-2 max-h-48 overflow-y-auto border border-gray-300 rounded-lg p-3">
                        {selectedService.serviceOptions
                          .filter((opt: any) => watchedOptions.includes(opt.id))
                          .flatMap((opt: any) => opt.features || [])
                          .map((feature: any) => (
                            <label key={feature.id} className="flex items-start cursor-pointer">
                              <input
                                type="checkbox"
                                checked={watchedFeatures.includes(feature.id)}
                                onChange={() => toggleFeature(feature.id)}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
                              />
                              <div className="ml-3 flex-1">
                                <p className="text-sm font-medium text-gray-900">{feature.name}</p>
                                {feature.description && (
                                  <p className="text-xs text-gray-500 mt-1">{feature.description}</p>
                                )}
                                <p className="text-xs text-gray-400 mt-1">
                                  {feature.isIncluded ? 'Included' : 'Optional'}
                                </p>
                              </div>
                            </label>
                          ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Right Column - Project Details */}
                <div className="space-y-6">
                  {/* Budget */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Budget Range
                    </label>
                    <Controller
                      name="budget"
                      control={control}
                      render={({ field }) => (
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <CurrencyDollarIcon className="h-5 w-5 text-gray-400" />
                          </div>
                          <input
                            {...field}
                            type="number"
                            min="1"
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter your budget"
                          />
                        </div>
                      )}
                    />
                    {errors.budget && (
                      <p className="mt-1 text-sm text-red-600">{errors.budget.message}</p>
                    )}
                    {selectedService && (
                      <p className="mt-1 text-sm text-gray-500">
                        Estimated cost: ${calculateEstimatedCost().toLocaleString()}
                      </p>
                    )}
                  </div>

                  {/* Timeline */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Project Timeline
                    </label>
                    <Controller
                      name="timeline"
                      control={control}
                      render={({ field }) => (
                        <select
                          {...field}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">Select timeline</option>
                          <option value="1-2 weeks">1-2 weeks</option>
                          <option value="3-4 weeks">3-4 weeks</option>
                          <option value="1-2 months">1-2 months</option>
                          <option value="3-6 months">3-6 months</option>
                          <option value="6+ months">6+ months</option>
                          <option value="flexible">Flexible</option>
                        </select>
                      )}
                    />
                    {errors.timeline && (
                      <p className="mt-1 text-sm text-red-600">{errors.timeline.message}</p>
                    )}
                  </div>

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Project Description
                    </label>
                    <Controller
                      name="description"
                      control={control}
                      render={({ field }) => (
                        <textarea
                          {...field}
                          rows={4}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Describe your project requirements, goals, and any specific needs..."
                        />
                      )}
                    />
                    {errors.description && (
                      <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                    )}
                  </div>

                  {/* Contact Preference */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Preferred Contact Method
                    </label>
                    <Controller
                      name="contactPreference"
                      control={control}
                      render={({ field }) => (
                        <div className="space-y-2">
                          {[
                            { value: 'email', label: 'Email' },
                            { value: 'phone', label: 'Phone Call' },
                            { value: 'meeting', label: 'Video Meeting' }
                          ].map((option) => (
                            <label key={option.value} className="flex items-center">
                              <input
                                type="radio"
                                value={option.value}
                                checked={field.value === option.value}
                                onChange={field.onChange}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                              />
                              <span className="ml-2 text-sm text-gray-700">{option.label}</span>
                            </label>
                          ))}
                        </div>
                      )}
                    />
                  </div>

                  {/* Urgency */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Urgency Level
                    </label>
                    <Controller
                      name="urgency"
                      control={control}
                      render={({ field }) => (
                        <select
                          {...field}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="low">Low - No rush</option>
                          <option value="medium">Medium - Standard timeline</option>
                          <option value="high">High - ASAP</option>
                        </select>
                      )}
                    />
                  </div>
                </div>
              </div>

              {/* Submit Buttons */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? 'Submitting...' : 'Submit Request'}
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}
